#include "proxy/data_flow.h"
#include "common/protocol.h"
#include "common/hex_utils.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>

// External event callbacks (declared in pipes.c)
extern void sitp_to_tcp_pipe_callback(int fd, short event, void *arg);
extern void tcp_to_sitp_pipe_callback(int fd, short event, void *arg);

// Internal functions
static void handle_sitp_to_tcp_data(proxy_data_flow_t *flow, const uint8_t *buffer, size_t len);
static void handle_tcp_to_sitp_data(proxy_data_flow_t *flow, const uint8_t *buffer, size_t len);
static void send_disconnect_notification(proxy_data_flow_t *flow, uint32_t connection_id);

static proxy_data_flow_t *global_data_flow = NULL;

int proxy_data_flow_init(proxy_data_flow_t *flow, tcp_client_manager_t *tcp_manager,
                        proxy_sitp_client_t *sitp_client, proxy_pipes_t *pipes,
                        proxy_config_t *config) {
    flow->tcp_manager = tcp_manager;
    flow->sitp_client = sitp_client;
    flow->pipes = pipes;
    flow->config = config;
    
    global_data_flow = flow;
    
    log_info("Proxy data flow initialized");
    return 0;
}

void proxy_data_flow_cleanup(proxy_data_flow_t *flow) {
    global_data_flow = NULL;
}

// Pipe callback implementations
void sitp_to_tcp_pipe_callback(int fd, short event, void *arg) {
    proxy_pipes_t *pipes = (proxy_pipes_t*)arg;
    
    if (!global_data_flow) {
        log_error("Data flow not initialized");
        return;
    }
    
    uint8_t buffer[8192];
    ssize_t bytes_read = read(fd, buffer, sizeof(buffer));
    
    if (bytes_read > 0) {
        handle_sitp_to_tcp_data(global_data_flow, buffer, bytes_read);
    } else if (bytes_read < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
        log_error("Failed to read from sitp_to_tcp pipe: %s", strerror(errno));
    }
}

void tcp_to_sitp_pipe_callback(int fd, short event, void *arg) {
    proxy_pipes_t *pipes = (proxy_pipes_t*)arg;
    
    if (!global_data_flow) {
        log_error("Data flow not initialized");
        return;
    }
    
    uint8_t buffer[8192];
    ssize_t bytes_read = read(fd, buffer, sizeof(buffer));
    
    if (bytes_read > 0) {
        handle_tcp_to_sitp_data(global_data_flow, buffer, bytes_read);
    } else if (bytes_read < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
        log_error("Failed to read from tcp_to_sitp pipe: %s", strerror(errno));
    }
}

static void handle_sitp_to_tcp_data(proxy_data_flow_t *flow, const uint8_t *buffer, size_t len) {
    // Extract message from padded buffer
    message_t *msg = extract_message_from_padded(buffer, len, flow->config->padding_size);
    if (!msg) {
        log_error("Failed to extract message from SITP data");
        return;
    }
    
    if (flow->config->verbose) {
        log_debug("Processing SITP message: client_fd=%u, cmd=%d, data_len=%u",
                 msg->client_fd, msg->cmd, msg->data_len);
        if (msg->data_len > 0 && flow->config->verbose) {
            print_hexdump(msg->data, msg->data_len);
        }
    }
    
    switch (msg->cmd) {
        case CMD_DATA: {
            // Get or create TCP connection for this client_fd
            tcp_connection_t *conn = tcp_client_get_connection(flow->tcp_manager, msg->client_fd);
            if (!conn) {
                conn = tcp_client_create_connection(flow->tcp_manager, msg->client_fd);
                if (!conn) {
                    log_error("Failed to create TCP connection for client %u", msg->client_fd);
                    break;
                }
            }
            
            // Send data to TCP server
            if (tcp_client_send_data(conn, msg->data, msg->data_len) < 0) {
                log_error("Failed to send data to TCP server for client %u", msg->client_fd);
            }
            break;
        }
        
        case CMD_CONNECT: {
            // Create new TCP connection
            tcp_connection_t *conn = tcp_client_create_connection(flow->tcp_manager, msg->client_fd);
            if (!conn) {
                log_error("Failed to create TCP connection for client %u", msg->client_fd);
                // Send disconnect notification back to bridge
                send_disconnect_notification(flow, msg->client_fd);
            }
            break;
        }
        
        case CMD_DISCONNECT: {
            // Close TCP connection
            tcp_client_close_connection(flow->tcp_manager, msg->client_fd);
            log_info("Closed TCP connection for client %u", msg->client_fd);
            break;
        }
        
        case CMD_PING: {
            // Respond with PONG
            padded_message_t *pong = create_padded_message(msg->client_fd, CMD_PONG, 
                                                         NULL, 0, flow->config->padding_size);
            if (pong) {
                if (proxy_pipes_write_tcp_to_sitp(flow->pipes, pong->buffer, pong->total_size) < 0) {
                    log_error("Failed to send PONG response");
                }
                free_padded_message(pong);
            }
            break;
        }
        
        default:
            log_warn("Unknown command received: %d", msg->cmd);
            break;
    }
    
    free(msg);
}

static void handle_tcp_to_sitp_data(proxy_data_flow_t *flow, const uint8_t *buffer, size_t len) {
    // This function handles data that needs to be sent back to SITP
    // For now, we'll just send it as-is
    if (proxy_sitp_client_send(flow->sitp_client, buffer, len) < 0) {
        log_error("Failed to send data to SITP");
    }
}

static void send_disconnect_notification(proxy_data_flow_t *flow, uint32_t connection_id) {
    padded_message_t *disconnect_msg = create_padded_message(connection_id, CMD_DISCONNECT,
                                                           NULL, 0, flow->config->padding_size);
    if (disconnect_msg) {
        if (proxy_pipes_write_tcp_to_sitp(flow->pipes, disconnect_msg->buffer, disconnect_msg->total_size) < 0) {
            log_error("Failed to send disconnect notification for connection %u", connection_id);
        }
        free_padded_message(disconnect_msg);
    }
}

// Function to be called from tcp_client.c when TCP connection receives data
void proxy_tcp_data_received(uint32_t connection_id, const uint8_t *data, size_t len) {
    if (!global_data_flow) {
        return;
    }
    
    // Create protocol message with TCP data
    padded_message_t *msg = create_padded_message(connection_id, CMD_DATA, 
                                                 data, len, global_data_flow->config->padding_size);
    if (msg) {
        if (proxy_pipes_write_tcp_to_sitp(global_data_flow->pipes, msg->buffer, msg->total_size) < 0) {
            log_error("Failed to forward TCP data to SITP for connection %u", connection_id);
        }
        free_padded_message(msg);
    }
}

// Function to be called from tcp_client.c when TCP connection is closed
void proxy_tcp_connection_closed(uint32_t connection_id) {
    if (!global_data_flow) {
        return;
    }
    
    send_disconnect_notification(global_data_flow, connection_id);
}