#include "bridge/tcp_server.h"
#include "common/protocol.h"
#include "log.h"
#include <event2/bufferevent.h>
#include <event2/buffer.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static void tcp_client_free(tcp_client_t *client);
static void tcp_client_read_cb(struct bufferevent *bev, void *ctx);
static void tcp_client_event_cb(struct bufferevent *bev, short events, void *ctx);
static void tcp_accept_cb(struct evconnlistener *listener, evutil_socket_t fd,
                         struct sockaddr *sa, int socklen, void *user_data);
static void tcp_accept_error_cb(struct evconnlistener *listener, void *user_data);

int tcp_server_init(tcp_server_t *server, struct event_base *base, 
                   bridge_config_t *config, bridge_pipes_t *pipes) {
    memset(server, 0, sizeof(tcp_server_t));
    server->base = base;
    server->config = config;
    server->pipes = pipes;
    server->clients = NULL;
    server->running = 0;
    
    struct sockaddr_in sin;
    memset(&sin, 0, sizeof(sin));
    sin.sin_family = AF_INET;
    sin.sin_port = htons(config->listen_port);
    
    if (inet_pton(AF_INET, config->listen_ip, &sin.sin_addr) <= 0) {
        log_error("Invalid IP address: %s", config->listen_ip);
        return -1;
    }
    
    server->listener = evconnlistener_new_bind(base, tcp_accept_cb, server,
                                              LEV_OPT_REUSEABLE | LEV_OPT_CLOSE_ON_FREE,
                                              -1, (struct sockaddr*)&sin, sizeof(sin));
    if (!server->listener) {
        log_error("Failed to create TCP listener on %s:%d", 
                  config->listen_ip, config->listen_port);
        return -1;
    }
    
    evconnlistener_set_error_cb(server->listener, tcp_accept_error_cb);
    
    log_info("TCP server initialized on %s:%d", config->listen_ip, config->listen_port);
    return 0;
}

int tcp_server_start(tcp_server_t *server) {
    server->running = 1;
    log_info("TCP server started");
    return 0;
}

void tcp_server_stop(tcp_server_t *server) {
    server->running = 0;
    log_info("TCP server stopped");
}

void tcp_server_cleanup(tcp_server_t *server) {
    tcp_client_t *client = server->clients;
    while (client) {
        tcp_client_t *next = client->next;
        tcp_client_free(client);
        client = next;
    }
    server->clients = NULL;
    
    if (server->listener) {
        evconnlistener_free(server->listener);
        server->listener = NULL;
    }
    
    log_info("TCP server cleaned up");
}

tcp_client_t *tcp_server_find_client(tcp_server_t *server, int fd) {
    tcp_client_t *client = server->clients;
    while (client) {
        if (client->fd == fd) {
            return client;
        }
        client = client->next;
    }
    return NULL;
}

static void tcp_client_free(tcp_client_t *client) {
    if (client->bev) {
        bufferevent_free(client->bev);
    }
    
    tcp_server_t *server = client->server;
    if (server->clients == client) {
        server->clients = client->next;
    } else {
        tcp_client_t *prev = server->clients;
        while (prev && prev->next != client) {
            prev = prev->next;
        }
        if (prev) {
            prev->next = client->next;
        }
    }
    
    padded_message_t *pmsg = create_padded_message(client->fd, CMD_DISCONNECT, 
                                                  NULL, 0, server->config->padding_size);
    if (pmsg) {
        ssize_t written = write(server->pipes->tcp_to_sitp_fd[1], pmsg->buffer, pmsg->total_size);
        if (written != (ssize_t)pmsg->total_size) {
            log_warn("Failed to write disconnect message to pipe");
        }
        free_padded_message(pmsg);
    }
    
    log_info("Client disconnected: fd=%d", client->fd);
    free(client);
}

static void tcp_client_read_cb(struct bufferevent *bev, void *ctx) {
    tcp_client_t *client = (tcp_client_t *)ctx;
    tcp_server_t *server = client->server;
    struct evbuffer *input = bufferevent_get_input(bev);
    
    size_t len = evbuffer_get_length(input);
    if (len == 0) return;
    
    uint8_t *data = malloc(len);
    if (!data) {
        log_error("Failed to allocate memory for client data");
        return;
    }
    
    evbuffer_remove(input, data, len);
    
    padded_message_t *pmsg = create_padded_message(client->fd, CMD_DATA, 
                                                  data, len, server->config->padding_size);
    if (pmsg) {
        ssize_t written = write(server->pipes->tcp_to_sitp_fd[1], pmsg->buffer, pmsg->total_size);
        if (written != (ssize_t)pmsg->total_size) {
            log_warn("Failed to write data message to pipe");
        }
        free_padded_message(pmsg);
    }
    
    free(data);
    log_debug("Received %zu bytes from client fd=%d", len, client->fd);
}

static void tcp_client_event_cb(struct bufferevent *bev, short events, void *ctx) {
    tcp_client_t *client = (tcp_client_t *)ctx;
    
    if (events & BEV_EVENT_ERROR) {
        log_error("Client connection error: fd=%d", client->fd);
    }
    if (events & BEV_EVENT_EOF) {
        log_info("Client connection closed: fd=%d", client->fd);
    }
    if (events & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
        tcp_client_free(client);
    }
}

static void tcp_accept_cb(struct evconnlistener *listener, evutil_socket_t fd,
                         struct sockaddr *sa, int socklen, void *user_data) {
    tcp_server_t *server = (tcp_server_t *)user_data;
    
    tcp_client_t *client = malloc(sizeof(tcp_client_t));
    if (!client) {
        log_error("Failed to allocate memory for new client");
        close(fd);
        return;
    }
    
    memset(client, 0, sizeof(tcp_client_t));
    client->fd = fd;
    client->server = server;
    
    client->bev = bufferevent_socket_new(server->base, fd, BEV_OPT_CLOSE_ON_FREE);
    if (!client->bev) {
        log_error("Failed to create bufferevent for client");
        free(client);
        close(fd);
        return;
    }
    
    bufferevent_setcb(client->bev, tcp_client_read_cb, NULL, tcp_client_event_cb, client);
    bufferevent_enable(client->bev, EV_READ | EV_WRITE);
    
    client->next = server->clients;
    server->clients = client;
    
    struct sockaddr_in *sin = (struct sockaddr_in *)sa;
    log_info("New client connected: fd=%d, ip=%s", fd, inet_ntoa(sin->sin_addr));
    
    padded_message_t *pmsg = create_padded_message(client->fd, CMD_CONNECT, 
                                                  NULL, 0, server->config->padding_size);
    if (pmsg) {
        ssize_t written = write(server->pipes->tcp_to_sitp_fd[1], pmsg->buffer, pmsg->total_size);
        if (written != (ssize_t)pmsg->total_size) {
            log_warn("Failed to write connect message to pipe");
        }
        free_padded_message(pmsg);
    }
}

static void tcp_accept_error_cb(struct evconnlistener *listener, void *user_data) {
    struct event_base *base = evconnlistener_get_base(listener);
    int err = EVUTIL_SOCKET_ERROR();
    log_error("TCP accept error: %s", evutil_socket_error_to_string(err));
    event_base_loopexit(base, NULL);
}

void tcp_server_close_client(tcp_server_t *server, tcp_client_t *client) {
    if (!server || !client) {
        return;
    }
    
    log_info("Closing TCP client fd=%d", client->fd);
    
    // Send disconnect notification to SITP
    padded_message_t *pmsg = create_padded_message(client->fd, CMD_DISCONNECT, 
                                                  NULL, 0, server->config->padding_size);
    if (pmsg) {
        ssize_t written = write(server->pipes->tcp_to_sitp_fd[1], pmsg->buffer, pmsg->total_size);
        if (written != (ssize_t)pmsg->total_size) {
            log_warn("Failed to write disconnect message to pipe");
        }
        free_padded_message(pmsg);
    }
    
    // Remove from client list and free
    tcp_client_free(client);
}