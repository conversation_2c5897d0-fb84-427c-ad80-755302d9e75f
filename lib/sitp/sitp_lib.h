#ifndef __SITP_LIB_H__
#define __SITP_LIB_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stddef.h>
#include <stdint.h>

/**
 * @brief Callback function for receiving data from named pipe
 *
 * This function is called whenever data is read from the SITP communication pipe.
 *
 * @param arg User-defined context parameter passed during sitp_lib_add call
 * @param buffer Pointer to received data buffer
 * @param len Number of bytes received
 */
typedef void (*sitp_lib_recv_data_func)(void *arg, uint8_t *buffer, size_t len);

/**
 * @brief Initialize SITP interface for inter-process communication
 *
 * Creates and configures a communication channel using named pipes.
 * Sets up libevent-based asynchronous I/O for data reception.
 *
 * @param eth_dev Reserved parameter (currently unused in implementation)
 * @param mtu Maximum transmission unit size for data packets
 * @param protocol Protocol identifier for SITP communication
 * @param local_id Local endpoint identifier used to determine pipe connection direction
 * @param remote_id Remote endpoint identifier used to determine pipe connection direction
 * @param local_port Reserved parameter (currently unused in implementation)
 * @param remote_port Reserved parameter (currently unused in implementation)
 * @param pfn_recv Callback function called when data is received from the pipe
 * @param arg User-defined context parameter passed to the receive callback
 * @return Pointer to initialized interface structure, or NULL on failure
 */
void *sitp_lib_add(const char *eth_dev, int mtu, uint16_t protocol,
                   uint16_t local_id, uint16_t remote_id, uint16_t local_port,
                   uint16_t remote_port, sitp_lib_recv_data_func pfn_recv,
                   void *arg);

/**
 * @brief Send data through the named pipe communication channel
 *
 * Writes data to the write end of the SITP pipe.
 * Handles reconnection automatically if the write pipe is not available.
 *
 * @param obj Interface pointer returned by sitp_lib_add
 * @param buffer Pointer to data to be sent
 * @param len Number of bytes to send
 * @return Number of bytes written on success, negative value on error,
 *         or len when simulating successful send (no reader available yet)
 */
int sitp_lib_send(void *obj, uint8_t *buffer, size_t len);

/**
 * @brief Start the SITP event processing loop
 *
 * Runs the libevent event loop to handle asynchronous I/O operations.
 * Processes incoming data from named pipes and handles reconnections automatically.
 * This function normally runs indefinitely until sitp_lib_stop() is called.
 *
 * @return 0 on normal shutdown, negative value on initialization failure
 */
int sitp_lib_start();

#ifdef __cplusplus
}
#endif

#endif